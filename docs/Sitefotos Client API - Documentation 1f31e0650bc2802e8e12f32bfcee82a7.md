# Sitefotos Client API - Documentation

**Version:** 0.9 (Draft)
**Date:** August 5th, 2025
**Status:** Planning

## 1. Introduction

Welcome to the Sitefotos Client API. This API allows client integrators (such as DevRabbit for JLL/USPS) to integrate with the Sitefotos platform to discover vendors, sites, and trades, manage webhook subscriptions, and submit work orders. The specific client context (e.g., JLL/USPS) is determined by the authentication headers provided. This document provides guidelines on how to use this API.

**Important Note on Identifiers:** Throughout this API, `sitefotos_internal_vendor_id` and `sitefotos_internal_site_id` are opaque, hashed identifiers provided by Sitefotos. These should be treated as strings and used as-is in subsequent API calls or for mapping purposes. They are not direct database IDs.

**Timestamp Convention:** All timestamp fields in request and response payloads are Unix timestamps (integer, seconds since the Unix epoch).

**Naming Convention:** All JSON field names (keys) in request and response payloads use `snake_case`.

## 1.1. Changelog

### Version 0.9 (August 5th, 2025)

**New Features:**
- **Form Template Updates**: Added support for updating work order form templates via the Update Work Order API
  - New `form_template_id` field in PATCH `/workorders/{id}` request body
  - Supports both standalone form template updates and combined updates with other fields

**API Enhancements:**
- **Consistent Response Format**: All work order endpoints now include `form_template_id` in responses:
  - Create Work Order: Enhanced response with complete work order details including form template
  - List Work Orders: Added `form_template_id` to each work order in the list
  - Get Work Order by External ID: Added `form_template_id` to response
  - Update Work Order: Added `form_template_id` to response
- **Webhook Updates**: Added `form_template_id` to `client_wo_submission` webhook payload


---

## 2. Base URLs

- **Client-specific data endpoints (vendors, sites, trades, workorders):**
    
    ```
    https://sitefotos.com/v1/api/client-view/
    ```
    
- **General webhook management (global across clients):**
    
    ```
    https://sitefotos.com/v1/api/
    ```
    

---

## 3. Authentication

Authentication for all API calls is handled via HTTP headers. Every API request must include:

- `x-api-key`: Your unique API key.
- `x-access-code`: The access code identifying the specific client account context (e.g., JLL/USPS).

**Example Headers:**

```
x-api-key: YOUR_ASSIGNED_API_KEY
x-access-code: YOUR_CLIENT_ACCESS_CODE
```

**Authentication Errors:**
If authentication fails, the API will respond with a `401 Unauthorized` status code and a JSON body:

```json
{ "message": "Invalid Access Code or API Key." }
```

---

## 4. Rate Limiting

- **Limit:** 50 requests per minute per client.
- **Response on limit exceed:** `429 Too Many Requests` with:
    
    ```json
    { "message": "Rate limit exceeded." }
    ```
    

---

## 5. Request and Response Format

All requests and responses use `application/json`.

---

## 6. Standard Error Responses

All error responses use the HTTP status code and a JSON body:

```json
{ "message": "Description of error." }
```

**Examples:**

- **400 Bad Request**`{ "message": "Malformed JSON in request body." }`
- **401 Unauthorized**`{ "message": "Invalid Access Code or API Key." }`
- **404 Not Found**`{ "message": "Vendor not found." }`
- **429 Too Many Requests**`{ "message": "Rate limit exceeded." }`
- **500 Internal Server Error**`{ "message": "Unexpected server error." }`

---

## 7. **Discovery & Mapping (Initial Setup / Periodic Refresh)**

**Base URL:** `https://sitefotos.com/v1/api/client-view/`

### 7.1. Vendor Discovery API

- **Path:** `GET /vendors`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Response (200 OK):** Array of vendor objects.
    
    ```json
    [
      {
        "sitefotos_internal_vendor_id": "bxL8K25vP",
        "vendor_name": "Arctic Plow Pros",
        "contact_email": "<EMAIL>",
        "phone_number": "55555501"
      }
    ]
    ```
    

### 7.2. Site Discovery API

- **Path:** `GET /sites`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Query Parameters:** `sitefotos_internal_vendor_id` (string, optional)
- **Response (200 OK):** Array of site mapping objects.
    
    ```json
    [
      {
        "primary_site_identifier": "NIP-BAY7",
        "sitefotos_internal_vendor_id": "bxL8K25vP",
        "sitefotos_internal_site_id": "s1a2b3c4d",
        "site_name": "Northwind Industrial Park - Bay 7",
        "address_line1": "100 Frozen Lane",
        "city": "Snowville",
        "state": "NT",
        "zip_code": "90001"
      }
    ]
    ```
    

### 7.3. Trade Discovery API

- **Path:** `GET /trades`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Response (200 OK):** Array of trade objects.
    
    ```json
    [
      { "trade_id": 1, "trade_name": "Snow removal" },
      { "trade_id": 2, "trade_name": "Janitorial services" }
    ]
    ```
    

### 7.4. Service Type Discovery API

- **Path:** `GET /service-types`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Response (200 OK):** Array of service types.
    
    ```json
    [
      { "service_type_id": 100, "name": "Push Walks", "trade_id": 1 },
      { "service_type_id": 101, "name": "Push Lots", "trade_id": 1 }
    ]
    ```

### 7.5. Work Order Form Template Discovery API

- **Path:** `GET /workorder-form-templates`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Response (200 OK):** Array of form template objects.
    
    ```json
    [
      {
        "template_id": "a1b2c3d4",
        "template_name": "Standard Workorder",
        "trade_id": 1
      }
    ]
    ```

## 8. Work Order Processing (Ongoing)

**Base URL:** `https://sitefotos.com/v1/api/client-view/`

### 8.1 Create a Workorder

- **Path:** `POST /workorders`
- **Method:** `POST`
- **Headers:** (Standard Authentication, `Content-Type: application/json`)
- **Request Body:**
    - `sitefotos_internal_vendor_id` (string, required)
    - `sitefotos_internal_site_id` (string, required)
    - `work_order_details` (object):
        - `external_wo_id` (string, required)
        - `description` (string, optional)
        - `trade_id` (integer)
        - `scheduled_date` (integer, optional)
        - `services` (array of objects, optional): each with `service_name` (string) and `service_type_id` (integer). Required if `form_template_id` is not provided.
        - `close_on_submit` (bool, optional; default: `false`)
        - `form_template_id` (string, optional): The encoded ID of a form template to use for the work order. If provided, the `services` array may be omitted.
    
    ```json
    {
      "sitefotos_internal_vendor_id": "bxL8K25vP",
      "sitefotos_internal_site_id": "s1a2b3c4d",
      "work_order_details": {
        "external_wo_id": "SNOW-ORD-2025-00123",
        "description": "Heavy snowfall overnight. Clear all parking areas and salt main walkways.",
        "trade_id": 1,
        "scheduled_date": 1747987200,
        "services": [
          { "service_name": "Plow Parking Lots", "service_type_id": 100 },
          { "service_name": "Salt Walkways", "service_type_id": 103 }
        ],
        "close_on_submit": false
      }
    }
    ```
    
- **Response (201 Created):**

    ```json
    {
      "sitefotos_work_order_id": "hashed_wo_id_example_102345",
      "external_wo_id": "SNOW-ORD-2025-00123",
      "status": "Scheduled",
      "description": "Heavy snowfall overnight. Clear all parking areas and salt main walkways.",
      "scheduled_date": 1747987200,
      "form_template_id": "FT789012",
      "status_message": "Work order received and queued for processing."
    }
    ```
    

### 8.2. Get Work Order by External ID

- **Path:** `GET /workorders/by-external-id/{external_wo_id}`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Path Parameters:**
    - `external_wo_id` (string, required): The unique external identifier for the work order.
- **Response (200 OK):**
    ```json
    {
      "sitefotos_work_order_id": "hashed_wo_id_1",
      "external_wo_id": "SNOW-ORD-2025-00123",
      "status": "OPEN",
      "description": "Heavy snowfall overnight. Clear all parking areas and salt main walkways.",
      "scheduled_date": 1747987200,
      "sitefotos_internal_site_id": "hashed_site_id_abc",
      "primary_site_identifier": "NIP-BAY7",
      "trade_name": "Snow Removal",
      "vendor_name": "Arctic Plow Pros",
      "form_template_id": "FT789012"
    }
    ```
- **Error Responses:**
    - `404 Not Found`: If no work order matches the provided `external_wo_id` for the authenticated client.

### 8.3. List Work Orders

Retrieves a paginated list of work orders for the authenticated client, with options for filtering and sorting.

- **Path:** `GET /workorders`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Query Parameters:**
    - `page` (integer, optional, default: 1): Page number for pagination.
    - `limit` (integer, optional, default: 25): Number of items per page (max 100).
    - `status` (string, optional, enum: `SCHEDULED`, `OPEN`, `COMPLETED`, `CANCELLED`): Filter by work order status.
    - `modified_at_from` (integer, optional, Unix timestamp): Filter work orders modified on or after this timestamp.
    - `modified_at_to` (integer, optional, Unix timestamp): Filter work orders modified on or before this timestamp.
    - `sitefotos_internal_vendor_id` (string, optional): Filter by the performing vendor's Sitefotos ID (obtained from Vendor Discovery API).
    - `sitefotos_internal_site_id` (string, optional): Filter by the Sitefotos Site ID (obtained from Site Discovery API).
    - `sort_by` (string, optional, enum: `last_modified`, default: `last_modified`): Field to sort by. Currently only `last_modified` is supported.
    - `sort_order` (string, optional, enum: `asc`, `desc`, default: `desc`): Sort order.
- **Response (200 OK):**
    ```json
    {
      "data": [
        {
          "sitefotos_work_order_id": "hashed_wo_id_1",
          "external_wo_id": "SNOW-ORD-2025-00123",
          "status": "OPEN",
          "description": "Heavy snowfall overnight. Clear all parking areas and salt main walkways.",
          "scheduled_date": 1747987200,
          "sitefotos_internal_site_id": "hashed_site_id_abc",
          "primary_site_identifier": "NIP-BAY7",
          "trade_name": "Snow Removal",
          "vendor_name": "Arctic Plow Pros",
          "form_template_id": "FT789012"
        }
      ],
      "metadata": {
        "pagination": {
          "total_items": 50,
          "total_pages": 2,
          "current_page": 1,
          "page_size": 25
        }
      }
    }
    ```
- **Example Request:**
    ```
    GET https://sitefotos.com/v1/api/client-view/workorders?status=OPEN&modified_at_from=1747900000&sort_by=last_modified&sort_order=desc&page=1&limit=10
    ```
### 8.4. Update Work Order

Allows modifying key work order fields after creation.

- **PATCH** `/client-view/workorders/{sitefotos_work_order_id}`
- **Path Param:** `sitefotos_work_order_id` (string)
- **Body:** at least one of:
    - `status` (string, optional, enum: `SCHEDULED`, `OPEN`, `COMPLETED`, `CANCELLED`)
    - `scheduled_date` (integer, optional)
    - `external_wo_id` (string, optional)
    - `description` (string, optional)
    - `form_template_id` (string, optional): The encoded ID of a form template to use for the work order

    ```json
    {
      "status":          "OPEN",
      "scheduled_date":  1748073600,
      "description":     "Reschedule to May 23, salt main entrance.",
      "form_template_id": "FT789012"
    }
    ```

- **Response (200):** updated work order summary
    ```json
    {
      "sitefotos_work_order_id": "hashed_wo_id_example_102345",
      "external_wo_id": "SNOW-ORD-2025-00123",
      "status": "Open",
      "description": "Reschedule to May 23, salt main entrance.",
      "scheduled_date": 1748073600,
      "form_template_id": "FT789012"
    }
    ```

#### Form Template Updates

When updating the `form_template_id` field:

1. **Validation**: The form template must exist and belong to the client vendor
2. **Combined Updates**: Form template updates can be combined with other field updates in a single request

**Example: Update only form template**
```json
{
  "form_template_id": "FT789012"
}
```

**Example: Update form template with other fields**
```json
{
  "form_template_id": "FT789012",
  "description": "Updated work order with new form template",
  "status": "OPEN"
}
```

### 8.5. Send Message to Vendor

Allows sending a message to the performing vendor for a specific work order via email.

- **Path:** `POST /workorders/send-message`
- **Method:** `POST`
- **Headers:** (Standard Authentication)
- **Request Body:**

    ```json
    {
      "sitefotos_work_order_id": "hashed_wo_id_example_102345",
      "message": "Please provide an update on the work order status."
    }
    ```

    **OR**

    ```json
    {
      "external_wo_id": "SNOW-ORD-2025-00123",
      "message": "The site access has been arranged for tomorrow."
    }
    ```

- **Request Body Fields:**
    - `sitefotos_work_order_id` (string, optional): The hashed internal work order ID
    - `external_wo_id` (string, optional): The external work order ID
    - `message` (string, required): The message content to send to the vendor

    **Note:** Either `sitefotos_work_order_id` OR `external_wo_id` must be provided, but not both.

- **Response (200 OK):**

    ```json
    {
      "sitefotos_work_order_id": "hashed_wo_id_example_102345",
      "external_wo_id": "SNOW-ORD-2025-00123",
      "status_message": "Message sent successfully to performing vendor."
    }
    ```

- **Error Responses:**
    - `400 Bad Request`: Invalid request body or validation errors
    - `404 Not Found`: Work order not found or vendor email not available
    - `500 Internal Server Error`: Failed to send email

**Email Behavior:**
- **Email Subject**: "Message from [Client Company] about Work Order [ID]"
- **Email Content**: Includes the client company name, work order ID, and message content

---

## 9. Webhook Configuration API

**Base URL:** `https://sitefotos.com/v1/api/`

### 9.1. Create Webhook Subscription

- **Path:** `POST /webhook`
- **Method:** `POST`
- **Headers:** (Standard Authentication, `Content-Type: application/json`)
- **Request Body:**
    - `url` (string, required)
        - `event` (string, enum, one-of): `client_checkin`, `client_checkout`, `client_wo_submission`
    
    ```json
    {
      "url": "https://integrator.example.com/sitefotos-client-webhook",
      "event": "client_wo_submission"
    }
    ```
    
- **Response (201 Created):**
    
    ```json
    {
      "webhook_id": "wh_uuid_123abc789xyz",
      "secret": "whsec_a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6"
    }
    ```
    
    **Important:** The `secret` field is returned only once upon creation. You must store this value securely, as it is required to validate incoming webhooks and cannot be retrieved again.
    

### 9.2. List Webhook Subscriptions

- **Path:** `GET /webhook`
- **Method:** `GET`
- **Headers:** (Standard Authentication)
- **Response (200 OK):** Array of webhook objects.
    
    ```json
    [
      {
        "webhook_id": "wh_uuid_123abc789xyz",
        "url": "https://integrator.example.com/sitefotos-client-webhook",
        "event": "client_checkin"
      }
    ]
    ```
    

### 9.3. Update Webhook Subscription

- **Path:** `PUT /webhook/{webhook_id}`
- **Method:** `PUT`
- **Headers:** (Standard Authentication, `Content-Type: application/json`)
- **Request Body:** `url` (required), `event` (required)
    
    ```json
    {
      "url": "https://integrator.example.com/updated-webhook",
      "event": "client_wo_submission"
    }
    ```
    
- **Response (200 OK):**
    
    ```json
    {  "webhook_id": "wh_uuid_123abc789xyz"}
    ```
    

### 9.4. Delete Webhook Subscription

- **Path:** `DELETE /webhook/{webhook_id}`
- **Method:** `DELETE`
- **Headers:** (Standard Authentication)
- **Response:** `204 No Content`

---

## 10. Outbound Webhook (Sitefotos to Integrator): Client Work Order Updates

Sitefotos sends `POST` requests to the subscribed URL when events occur.

### 10.1. Webhook Security and Signature Validation

To ensure the integrity and authenticity of webhook events, all outgoing `POST` requests from Sitefotos include a special signature in the HTTP headers. You must validate this signature to confirm that the webhook was sent by Sitefotos and was not tampered with.

- **Header:** `X-Sitefotos-Signature`
- **Algorithm:** HMAC-SHA256

**Validation Steps:**

1.  **Retrieve the Secret:** Use the `secret` you stored when you created the webhook subscription.
2.  **Get the Raw Body:** Use the raw, unmodified string of the request body. It is critical not to parse and then re-stringify the JSON, as this can alter the signature.
3.  **Compute Signature:** Calculate the HMAC-SHA256 signature of the raw request body using your stored `secret`.
4.  **Compare:** Securely compare your computed signature with the value from the `X-Sitefotos-Signature` header. If they match, the webhook is valid.


**Common Payload Fields:**

- `event` (string)
- `event_timestamp` (integer)
- `sitefotos_work_order_id` (string)
- `external_wo_id` (string)
- `sitefotos_internal_site_id` (string)
- `sitefotos_internal_vendor_id` (string)
- `primary_site_identifier` (string)
- `vendor_name` (string)
- `event_data` (object)

---
### 10.2. Event Type: `client_checkin`

```json
{
  "event": "client_checkin",
  "event_timestamp": 1747987080,
  "sitefotos_work_order_id": "hashed_wo_id_example_102345",
  "external_wo_id": "SNOW-ORD-2025-00123",
  "sitefotos_internal_site_id": "s1a2b3c4d",
  "sitefotos_internal_vendor_id": "bxL8K25vP",
  "primary_site_identifier": "NIP-BAY7",
  "event_data": { "location": { "latitude": 64.1466, "longitude": -21.9426 } }
}
```

### 10.3. Event Type: `client_checkout`

```json
{
  "event": "client_checkout",
  "event_timestamp": 1747996200,
  "sitefotos_work_order_id": "hashed_wo_id_example_102345",
  "external_wo_id": "SNOW-ORD-2025-00123",
  "sitefotos_internal_site_id": "s1a2b3c4d",
  "sitefotos_internal_vendor_id": "bxL8K25vP",
  "primary_site_identifier": "NIP-BAY7",
  "event_data": { "location": { "latitude": 64.1468, "longitude": -21.9429 } }
}
```

### 10.4. Event Type: `client_wo_submission`

```json
{
  "event": "client_wo_submission",
  "event_timestamp": 1748064000,
  "sitefotos_work_order_id": "hashed_wo_id_example_102345",
  "external_wo_id": "SNOW-ORD-2025-00123",
  "sitefotos_internal_site_id": "s1a2b3c4d",
  "sitefotos_internal_vendor_id": "bxL8K25vP",
  "primary_site_identifier": "NIP-BAY7",
  "form_template_id": "FT789012",
  "event_data": {
    "services": [
      { "service_name": "Plow Parking Lots", "status": "Completed" },
      { "service_name": "Salt Walkways", "status": "Completed" }
    ],
    "photos": [
      {
        "photo_url": "https://19b5bf454221a3009503-837ce6cc4941d82f7e9704a4735379b2.ssl.cf1.rackcdn.com/random.jpg"
      },
      {
        "photo_url": "https://19b5bf454221a3009503-837ce6cc4941d82f7e9704a4735379b2.ssl.cf1.rackcdn.com/random.jpg"
      }
    ]
  }
}
```