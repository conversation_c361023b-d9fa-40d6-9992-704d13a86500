const {awaitSafeQuery, insertObj, updateObj} = require('../../utils/db')
const {encodeID, decodeID} = require('../../utils/common')
const bus = require('../../utils/eventbus');
const { encodeIdShort, decodeIdShort } = require('../../utils/common');
const { deepSearchObject, deepSearchObjects } = require('../../utils/common');
const { generateSecret, generateHmacSignature } = require('../../utils/crypto');
const crypto = require('crypto');

const {getContractors} = require('../../utils/vendor');
const addWebhook = async (vendorId, data) => {
    const existingWebhook = await awaitSafeQuery('SELECT * FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_event = ? AND sw_active = 1', [vendorId, data.event]);
    if (existingWebhook.length > 0) {
        throw new Error('A webhook for this event already exists.');
    }
    const secret = generateSecret();
    const iData = {
        sw_url: data.url,
        sw_event: data.event,
        sw_vendor_id: vendorId,
        sw_secret: secret,
    }
    let inserted = await insertObj('sitefotos_webhooks', iData);
    let id = encodeID(inserted.insertId, 'sitefotos_webhooks');
    return { webhook_id: id, secret: secret };
    
};
const updateWebhook = async (id, vendorId, data) => {
    const decoded = decodeID(id);
    const test = await awaitSafeQuery(`SELECT * FROM sitefotos_webhooks WHERE sw_id = ? and sw_active = 1 and sw_vendor_id=?`, [decoded, vendorId]);
    if (test.length == 0) {
        return null;
    }

    if (data.event) {
        const existingWebhook = await awaitSafeQuery(
            'SELECT sw_id FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_event = ? AND sw_active = 1 AND sw_id != ?',
            [vendorId, data.event, decoded]
        );

        if (existingWebhook.length > 0) {
            throw new Error('A webhook for this event already exists.');
        }
    }

    const uData = {};
    if (data.url) {
        uData.sw_url = data.url;
    }
    if (data.event) {
        uData.sw_event = data.event;
    }

    if (Object.keys(uData).length > 0) {
        await updateObj('sitefotos_webhooks', uData, ['sw_id'], [decoded]);
    }

    return { webhook_id: id };
}
const deleteWebhook = async (id, vendorId) => {
    let decoded = decodeID(id);
    let test  = await awaitSafeQuery(`SELECT * FROM sitefotos_webhooks WHERE sw_id = ? and sw_active = 1 and sw_vendor_id=?`, [decoded, vendorId]);
    if(test.length == 0){
        return null;
    }
    const uData = {
        sw_active: 0
    }
    let updated = await updateObj('sitefotos_webhooks', uData, ['sw_id'], [decoded]);
    return true;
}

const getWebhooks = async (vendorId) => {
    let query = 'SELECT * FROM sitefotos_webhooks WHERE sw_vendor_id = ? and sw_active = 1';
    let webhooks = await awaitSafeQuery(query, [vendorId]);
    //remove db fields and add only id, url, event
    let webhooksArr = [];
    for (let i = 0; i < webhooks.length; i++) {
        let webhook = webhooks[i];
        let decoded = encodeID(webhook.sw_id, 'sitefotos_webhooks');
        webhooksArr.push({
            id: decoded,
            url: webhook.sw_url,
            event: webhook.sw_event,
        });
    }
    return webhooksArr;
}

test = async () => {
    let res = await addWebhook(1, {url: 'https://www.google.com', event: 'checkin'})
    console.log(res)
    console.log(decodeID(res.webhook_id))
    console.log(await getWebhooks(1))
    console.log(await updateWebhook(res.webhook_id, {url: 'https://www.yahoo.com', event: 'checkout'}))
    console.log(await deleteWebhook('a7AEd523-8498-dF58-A5D5-eE9A6Deab7Ee'))

}
const processUpdateWebhooks = async (system_id) => {
    try {
        let webhooks = await awaitSafeQuery(`SELECT sw_id, sw_url, sw_event, sw_vendor_id, sw_secret FROM sitefotos_webhooks WHERE sw_active = 1 and sw_event='workorders_updated'`);
        for (let i = 0; i < webhooks.length; i++) {
            let webhook = webhooks[i];
            let url = webhook.sw_url;
            let data = {
                event: "workorders_updated",
                system_id: system_id,
                timestamp: Math.floor(Date.now() / 1000), //send current unixtime in seconds
            }
            const body = JSON.stringify(data);
            const signature = generateHmacSignature(webhook.sw_secret, body);
            let response = await fetch(url, {
                method: 'POST',
                body: body,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Sitefotos-Signature': signature
                },
            });
            const responseBody = await response.text();
            console.log(response.status)
            let log = {
                swl_webhook_id: webhook.sw_id,
                swl_response: responseBody,
                swl_status_code: response.status,
                swl_event: 'workorders_updated',
                swl_payload: body
            }
            await insertObj('sitefotos_webhooks_logs', log);
        }
    }
    catch (e) {
        console.log(e)
    }

}
//This is currently added here for testing but can be over to more appropriate place later

bus.on('CHECKIN', async (data) => {
    let { timeStamp, formId, siteId, uploaderId, userId,lat,lng } = data;
    try {
        console.log('CHECKIN', timeStamp, formId, siteId);
        //check if a workorder is associated with this form
        let woData = await awaitSafeQuery(`SELECT sf_vendor_id,sf_form_workticket, sf_form_provider, sf_form_workticket_id, sf_form_workticket_template_id, sf_form_workticket_sc_pin,swo_internal_id, swo_external_id,swo_id from sitefotos_forms left join sitefotos_work_orders on sf_form_workticket_id=swo_id  where sf_id =?`, [formId], {useMainPool: true});
        if(woData && woData.length > 0){
            let clientSite = await awaitSafeQuery(`SELECT * from sitefotos_site_client_mapping where sscm_site_id=?`, [siteId], {useMainPool: true});
            if(clientSite && clientSite.length > 0){
                //this is a client site, so we need to trigger the client checkin webhook
                let {swo_id, swo_external_id, sf_vendor_id} = woData[0];
                let {sscm_client_site_id} = clientSite[0];
                let clientSiteData = await awaitSafeQuery(`SELECT * from sitefotos_client_sites where scs_id=?`, [sscm_client_site_id], {useMainPool: true});
                if(clientSiteData && clientSiteData.length > 0){
                    let {scs_client_vendor_id, scs_client_internal_id} = clientSiteData[0];
                    await processClientCheckin({
                        clientVendorId: scs_client_vendor_id,
                        timestamp: timeStamp,
                        workOrderId: swo_id,
                        externalWoId: swo_external_id,
                        siteId: siteId,
                        performingVendorId: sf_vendor_id,
                        primarySiteIdentifier: scs_client_internal_id,
                        eventDetails: { location: { latitude: lat, longitude: lng } }
                    });
                }
            }
        }
        //check if there are any webhooks associated with this vendor
       
        let webhooks = await awaitSafeQuery(`SELECT sw_id, sw_url, sw_event, sw_secret FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_active = 1 and sw_event='checkin'`, [userId]);
        
        //call the webhook with the data gathered
        if (webhooks && webhooks.length > 0) {
            let contractorId = null
            if(uploaderId != userId){
                const contractors = await getContractors(userId);
                let targetContractor = contractors.find(y => y.vendorId == uploaderId)
                if(targetContractor){
                    contractorId = targetContractor.id
                }
            }
            let workorderId = woData.length > 0 ? woData[0].swo_internal_id || woData[0].swo_id : null;
            for (let i = 0; i < webhooks.length; i++) {
                let webhook = webhooks[i];
                let url = webhook.sw_url;
                //call the webhook
                let data = {
                    event: "checkin",
                    timestamp: Math.floor(timeStamp).toString(),
                    form_id: formId,
                    site_id: siteId,
                    workorder_id: workorderId,
                    contact_id: contractorId,
                    lat: lat,
                    lng: lng,
                    workorder_external_id: woData.length > 0 ? woData[0].swo_external_id : null,
                }
                const body = JSON.stringify(data);
                const signature = generateHmacSignature(webhook.sw_secret, body);
                let response = await fetch(url, {
                    method: 'POST',
                    body: body,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Sitefotos-Signature': signature
                    },
                });
                const responseBody = await response.text();
                console.log(response.status)
                let log = {
                    swl_webhook_id: webhook.sw_id,
                    swl_response: responseBody,
                    swl_status_code: response.status,
                    swl_event: 'checkin',
                    swl_payload: body
                }
                await insertObj('sitefotos_webhooks_logs', log);

            }
        }
    } catch (e) {
        console.log(`Error in CHECKIN event: ${e}`)
    }
});

bus.on('CHECKOUT', async (data) => {
    let { timeStamp, formId, siteId, formSubmissionId,uploaderVid,userVid,lat,lng } = data;
    try {
        console.log('CHECKOUT', timeStamp, formId, siteId, formSubmissionId, lat, lng);
        //check if a workorder is associated with this form
        let woData = await awaitSafeQuery(`select swo_internal_id,swo_external_id,swo_id from sitefotos_work_orders_submissions left join sitefotos_work_orders on swos_workorder_id=swo_id where swos_form_submission_id = ?`, [formSubmissionId], {useMainPool: true});
        if(woData && woData.length > 0){
            let clientSite = await awaitSafeQuery(`SELECT * from sitefotos_site_client_mapping where sscm_site_id=?`, [siteId], {useMainPool: true});
            if(clientSite && clientSite.length > 0){
                //this is a client site, so we need to trigger the client checkout webhook
                let {swo_id, swo_external_id} = woData[0];
                let {sscm_client_site_id} = clientSite[0];
                let clientSiteData = await awaitSafeQuery(`SELECT * from sitefotos_client_sites where scs_id=?`, [sscm_client_site_id], {useMainPool: true});
                if(clientSiteData && clientSiteData.length > 0){
                    let {scs_client_vendor_id, scs_client_internal_id} = clientSiteData[0];
                    await processClientCheckout({
                        clientVendorId: scs_client_vendor_id,
                        timestamp: timeStamp,
                        workOrderId: swo_id,
                        externalWoId: swo_external_id,
                        siteId: siteId,
                        performingVendorId: uploaderVid,
                        primarySiteIdentifier: scs_client_internal_id,
                        eventDetails: { location: { latitude: lat, longitude: lng } }
                    });
                    //since this is a checkout on a work order, it is also a submission
                    let submissionData = await awaitSafeQuery(`SELECT sfs_form_data_full from sitefotos_forms_submitted where sfs_id=?`, [formSubmissionId], {useMainPool: true});
                    if(submissionData && submissionData.length > 0){
                        let eventDetails = {
                            services: [],
                            photos: []
                        };
                        try{
                            let formData = JSON.parse(submissionData[0].sfs_form_data_full);
                            const services = deepSearchObjects(formData, 'type', (k, v) => v === 'service');
                            const photos = deepSearchObjects(formData, 'type', (k, v) => v === 'file');
                            eventDetails.services = services.map(x => ({service_name: x.title, status: x.Completed == true ? 'Completed' : 'Not Completed'}));
                            photos.forEach((photo) => {
                                if(photo.value && photo.value.length > 0){
                                    photo.value.forEach((photoObj) => {
                                        eventDetails.photos.push({
                                            photo_url: photoObj.lrImageURL
                                        });
                                    });
                                }
                            })
                        }catch(e){
                            console.log('error parsing form data', e)
                        }
                        
                        await processClientWOSubmission({
                            clientVendorId: scs_client_vendor_id,
                            timestamp: timeStamp,
                            workOrderId: swo_id,
                            externalWoId: swo_external_id,
                            siteId: siteId,
                            performingVendorId: uploaderVid,
                            primarySiteIdentifier: scs_client_internal_id,
                            eventDetails: eventDetails
                        });
                    }
                }
            }
        }
        //check if there are any webhooks associated with this vendor
        let webhooks = await awaitSafeQuery(`SELECT sw_id, sw_url, sw_event, sw_secret FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_active = 1 and sw_event='checkout'`, [userVid], {useMainPool: true});
        //call the webhook with the data gathered
        if (webhooks && webhooks.length > 0) {
            let contractorId = null
            if(uploaderVid != userVid){
                const contractors = await getContractors(userVid);
                let targetContractor = contractors.find(y => y.vendorId == uploaderVid)
                if(targetContractor){
                    contractorId = targetContractor.id
                }
            }
            let workorderId = woData.length > 0 ? woData[0].swo_internal_id || woData[0].swo_id : null;
            for (let i = 0; i < webhooks.length; i++) {
                let webhook = webhooks[i];
                let url = webhook.sw_url;
                //call the webhook
                let data = {
                    event: "checkout",
                    timestamp: Math.floor(timeStamp).toString(),
                    form_id: formId,
                    site_id: siteId,
                    form_submission_id: formSubmissionId,
                    workorder_id: workorderId,
                    contact_id: contractorId,
                    workorder_external_id: woData.length > 0 ? woData[0].swo_external_id : null,
                    lat: lat,
                    lng: lng,
                }
                const body = JSON.stringify(data);
                const signature = generateHmacSignature(webhook.sw_secret, body);
                let response = await fetch(url, {
                    method: 'POST',
                    body: body,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Sitefotos-Signature': signature
                    },
                });
                //log the call and response in sitefotos_webhooks_logs table
                const responseBody = await response.text();
                let log = {
                    swl_webhook_id: webhook.sw_id,
                    swl_response: responseBody,
                    swl_status_code: response.status,
                    swl_event: 'checkout',
                    swl_payload: body
                }
                await insertObj('sitefotos_webhooks_logs', log);



            }
        }
    } catch (e) {
        console.log(`Error in CHECKOUT event: ${e}`)
    }
});
//test()

const processClientCheckin = async (eventData) => {
    const {
        clientVendorId, // ID of the subscribing client vendor
        timestamp, // Unix timestamp of the event
        workOrderId, // Raw DB ID of the work order
        externalWoId,
        siteId, // Raw DB ID of the site
        performingVendorId, // Raw DB ID of the vendor performing the work
        primarySiteIdentifier,
        eventDetails, // e.g., { location: { latitude, longitude } }
    } = eventData;
    try {
        console.log('CLIENT_CHECKIN event received for clientVendorId:', clientVendorId, eventData);
        // Fetch vendor name
        const vendorData = await awaitSafeQuery(`SELECT vendor_company_name FROM maptile_vendors WHERE vendor_id = ?`, [performingVendorId]);
        const vendorName = vendorData.length > 0 ? vendorData[0].vendor_company_name : null;

        const webhooks = await awaitSafeQuery(
           `SELECT sw_id, sw_url, sw_secret FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_active = 1 AND sw_event = 'client_checkin'`,
           [clientVendorId]
       );
        for (const webhook of webhooks) {
            const payload = {
                event: 'client_checkin',
                event_timestamp: timestamp,
                sitefotos_work_order_id: encodeIdShort(workOrderId, 'sitefotos_work_orders'),
                external_wo_id: externalWoId,
                sitefotos_internal_site_id: encodeIdShort(siteId, 'maptile_building'),
                sitefotos_internal_vendor_id: encodeIdShort(performingVendorId, 'maptile_vendors'),
                primary_site_identifier: primarySiteIdentifier,
                vendor_name: vendorName, // Added vendor_name
                event_data: eventDetails || {},
            };
           const body = JSON.stringify(payload);
           const signature = generateHmacSignature(webhook.sw_secret, body);
           const response = await fetch(webhook.sw_url, {
               method: 'POST',
               body: body,
               headers: {
                   'Content-Type': 'application/json',
                   'X-Sitefotos-Signature': signature
               },
           });
            const responseBody = await response.text();
            await insertObj('sitefotos_webhooks_logs', {
                swl_webhook_id: webhook.sw_id,
                swl_response: responseBody,
                swl_status_code: response.status,
                swl_event: payload.event,
                swl_payload: body,
            });
        }
    } catch (e) {
        console.error(`Error in CLIENT_CHECK_IN event processing for clientVendorId ${clientVendorId}:`, e, eventData);
    }
}
const processClientCheckout = async (eventData) => {
    const {
        clientVendorId,
        timestamp,
        workOrderId,
        externalWoId,
        siteId,
        performingVendorId,
        primarySiteIdentifier,
        eventDetails, // e.g., { location: { latitude, longitude } }
    } = eventData;
    try {
        console.log('CLIENT_CHECKOUT event received for clientVendorId:', clientVendorId, eventData);
        // Fetch vendor name
        const vendorData = await awaitSafeQuery(`SELECT vendor_company_name FROM maptile_vendors WHERE vendor_id = ?`, [performingVendorId]);
        const vendorName = vendorData.length > 0 ? vendorData[0].vendor_company_name : null;

        const webhooks = await awaitSafeQuery(
           `SELECT sw_id, sw_url, sw_secret FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_active = 1 AND sw_event = 'client_checkout'`,
           [clientVendorId]
       );
        for (const webhook of webhooks) {
            const payload = {
                event: 'client_checkout',
                event_timestamp: timestamp,
                sitefotos_work_order_id: encodeIdShort(workOrderId, 'sitefotos_work_orders'),
                external_wo_id: externalWoId,
                sitefotos_internal_site_id: encodeIdShort(siteId, 'maptile_building'),
                sitefotos_internal_vendor_id: encodeIdShort(performingVendorId, 'maptile_vendors'),
                primary_site_identifier: primarySiteIdentifier,
                vendor_name: vendorName, // Added vendor_name
                event_data: eventDetails || {},
            };
           const body = JSON.stringify(payload);
           const signature = generateHmacSignature(webhook.sw_secret, body);
           const response = await fetch(webhook.sw_url, {
               method: 'POST',
               body: body,
               headers: {
                   'Content-Type': 'application/json',
                   'X-Sitefotos-Signature': signature
               },
           });
            const responseBody = await response.text();
            await insertObj('sitefotos_webhooks_logs', {
                swl_webhook_id: webhook.sw_id,
                swl_response: responseBody,
                swl_status_code: response.status,
                swl_event: payload.event,
                swl_payload: body,
            });
        }
    } catch (e) {
        console.error(`Error in CLIENT_CHECK_OUT event processing for clientVendorId ${clientVendorId}:`, e, eventData);
    }
}

const processClientWOSubmission = async (eventData) => {
    const {
        clientVendorId,
        timestamp,
        workOrderId,
        externalWoId,
        siteId,
        performingVendorId,
        primarySiteIdentifier,
        eventDetails, // e.g., { services: [], photos: [] }
    } = eventData;
    try {
        console.log('CLIENT_WO_SUBMISSION event received for clientVendorId:', clientVendorId, eventData);
        // Fetch vendor name
        const vendorData = await awaitSafeQuery(`SELECT vendor_company_name FROM maptile_vendors WHERE vendor_id = ?`, [performingVendorId]);
        const vendorName = vendorData.length > 0 ? vendorData[0].vendor_company_name : null;

        const webhooks = await awaitSafeQuery(
           `SELECT sw_id, sw_url, sw_secret FROM sitefotos_webhooks WHERE sw_vendor_id = ? AND sw_active = 1 AND sw_event = 'client_wo_submission'`,
           [clientVendorId]
       );
        for (const webhook of webhooks) {
            const payload = {
                event: 'client_wo_submission',
                event_timestamp: timestamp,
                sitefotos_work_order_id: encodeIdShort(workOrderId, 'sitefotos_work_orders'),
                external_wo_id: externalWoId,
                sitefotos_internal_site_id: encodeIdShort(siteId, 'maptile_building'),
                sitefotos_internal_vendor_id: encodeIdShort(performingVendorId, 'maptile_vendors'),
                primary_site_identifier: primarySiteIdentifier,
                vendor_name: vendorName, // Added vendor_name
                event_data: eventDetails || { services: [], photos: [] },
            };
           const body = JSON.stringify(payload);
           const signature = generateHmacSignature(webhook.sw_secret, body);
           const response = await fetch(webhook.sw_url, {
               method: 'POST',
               body: body,
               headers: {
                   'Content-Type': 'application/json',
                   'X-Sitefotos-Signature': signature
               },
           });
            const responseBody = await response.text();
            await insertObj('sitefotos_webhooks_logs', {
                swl_webhook_id: webhook.sw_id,
                swl_response: responseBody,
                swl_status_code: response.status,
                swl_event: payload.event,
                swl_payload: body,
            });
        }
    } catch (e) {
        console.error(`Error in CLIENT_WO_SUBMISSION event processing for clientVendorId ${clientVendorId}:`, e, eventData);
    }
}

module.exports = {
    addWebhook,
    updateWebhook,
    deleteWebhook,
    getWebhooks,
    processUpdateWebhooks,
    processClientCheckin,
    processClientCheckout,
    processClientWOSubmission
};